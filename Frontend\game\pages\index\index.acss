.index-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部搜索栏 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.search-bar {
  flex: 1;
  margin-right: 20rpx;
}

.call-trainer {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
}

.thunder-icon {
  font-size: 24rpx;
  color: #ffffff;
  margin-right: 8rpx;
}

.call-text {
  font-size: 24rpx;
  color: #ffffff;
}

/* 游戏分类网格 */
.game-categories {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.category-item {
  width: 18%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

/* 热门游戏标签 */
.hot-games {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.hot-game-item {
  margin-right: 20rpx;
}

.hot-game-name {
  font-size: 28rpx;
  color: #1677ff;
  background-color: #f0f8ff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid #d6e4ff;
}

.more-games {
  margin-left: auto;
  background-color: #667eea;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.more-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 推荐大神区域 */
.recommended-section {
  background-color: #ffffff;
  padding: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.filter-options {
  display: flex;
  align-items: center;
}

.filter-icon {
  font-size: 24rpx;
  color: #999999;
  margin-right: 8rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #999999;
}

/* 陪玩师列表 */
.player-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.player-card {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 语音信息 */
.voice-info {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  z-index: 2;
}

.play-button {
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.play-icon {
  font-size: 24rpx;
  color: #ffffff;
}

.voice-duration {
  font-size: 20rpx;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  text-align: center;
}

/* 陪玩师头像 */
.player-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

/* 陪玩师信息 */
.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.player-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.player-badges {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.gender-icon {
  font-size: 24rpx;
}

.gender-icon.male {
  color: #1677ff;
}

.gender-icon.female {
  color: #ff69b4;
}

.hot-badge {
  display: flex;
  align-items: center;
  background-color: #fff2e8;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.fire-icon {
  font-size: 20rpx;
  color: #fa8c16;
  margin-right: 4rpx;
}

.hot-text {
  font-size: 20rpx;
  color: #fa8c16;
}

.player-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.player-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.player-price {
  font-size: 28rpx;
  color: #ff4d4f;
  font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .category-item {
    width: 20%;
  }
  
  .category-icon {
    width: 60rpx;
    height: 60rpx;
  }
  
  .category-name {
    font-size: 22rpx;
  }
}
