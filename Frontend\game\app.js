App({
  onLaunch(options) {
    console.log('小程序启动', options);
    // 初始化全局数据
    this.globalData = {
      userInfo: null,
      selectedGame: null,
      orderInfo: null
    };
  },
  
  onShow() {
    console.log('小程序显示');
  },
  
  onHide() {
    console.log('小程序隐藏');
  },
  
  onError(error) {
    console.error('小程序错误', error);
  },
  
  // 全局数据
  globalData: {
    userInfo: null,
    selectedGame: null,
    orderInfo: null
  },
  
  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      my.getAuthCode({
        scopes: ['auth_user'],
        success: (res) => {
          console.log('获取授权码成功', res);
          // 这里应该调用后端接口获取用户信息
          resolve(res);
        },
        fail: (err) => {
          console.error('获取授权码失败', err);
          reject(err);
        }
      });
    });
  },
  
  // 设置全局数据
  setGlobalData(key, value) {
    this.globalData[key] = value;
  },
  
  // 获取全局数据
  getGlobalData(key) {
    return this.globalData[key];
  }
});
