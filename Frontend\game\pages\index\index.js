Page({
  data: {
    searchValue: '',
    gameCategories: [
      { id: 1, name: '王者荣耀', icon: '/images/games/wzry.png' },
      { id: 2, name: '三角洲行动', icon: '/images/games/sjzxd.png' },
      { id: 3, name: '和平精英', icon: '/images/games/hpjy.png' },
      { id: 4, name: 'LOL手游', icon: '/images/games/lol.png' },
      { id: 5, name: '无畏契约', icon: '/images/games/wwqy.png' },
      { id: 6, name: '绝地求生', icon: '/images/games/jdqs.png' },
      { id: 7, name: '英雄联盟', icon: '/images/games/yxlm.png' },
      { id: 8, name: 'CSGO', icon: '/images/games/csgo.png' },
      { id: 9, name: '永劫无间', icon: '/images/games/yjwj.png' },
      { id: 10, name: '更多品类', icon: '/images/games/more.png' }
    ],
    hotGames: [
      { id: 1, name: '王者荣耀' },
      { id: 2, name: '三角洲行动' },
      { id: 3, name: '和平精英' }
    ],
    recommendedPlayers: [
      {
        id: 1,
        name: '雾眠',
        title: '大国...',
        gender: 'male',
        isHot: true,
        hotValue: '1.8万',
        avatar: '/images/players/wuming.jpg',
        voiceDuration: '28',
        description: '幽默男大声音温柔会唱歌，国服技术陪（精壮野射边）♥双区各段可接，巅峰...',
        price: 25
      },
      {
        id: 2,
        name: '嘉baby',
        title: '',
        gender: 'female',
        isHot: true,
        hotValue: '4.2千',
        avatar: '/images/players/jiababy.jpg',
        voiceDuration: '8',
        description: '板板好 王者荣耀q区王者段位最高40星 少女甜妹音 人皮话多不高冷 主玩中路...',
        price: 27
      },
      {
        id: 3,
        name: '瞳',
        title: '五国打野...',
        gender: 'male',
        isHot: true,
        hotValue: '1.4万',
        avatar: '/images/players/tong.jpg',
        voiceDuration: '15',
        description: '全平台最低价国服，只为赚包烟，无敌',
        price: 22
      }
    ]
  },

  onLoad() {
    console.log('首页加载');
    this.loadRecommendedPlayers();
  },

  onShow() {
    console.log('首页显示');
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // 搜索提交
  onSearchSubmit() {
    const searchValue = this.data.searchValue.trim();
    if (searchValue) {
      console.log('搜索:', searchValue);
      // 执行搜索逻辑
      this.performSearch(searchValue);
    }
  },

  // 执行搜索
  performSearch(keyword) {
    my.showLoading({
      content: '搜索中...'
    });

    // 模拟搜索API调用
    setTimeout(() => {
      my.hideLoading();
      my.showToast({
        type: 'success',
        content: `搜索"${keyword}"完成`
      });
    }, 1000);
  },

  // 呼叫陪练师
  onCallTrainer() {
    my.showModal({
      title: '呼叫陪练师',
      content: '此功能可以快速匹配在线陪练师',
      showCancel: true,
      confirmText: '立即呼叫',
      success: (res) => {
        if (res.confirm) {
          console.log('用户确认呼叫陪练师');
          this.callTrainer();
        }
      }
    });
  },

  // 呼叫陪练师逻辑
  callTrainer() {
    my.showLoading({
      content: '匹配中...'
    });

    setTimeout(() => {
      my.hideLoading();
      my.showToast({
        type: 'success',
        content: '匹配成功！'
      });
    }, 2000);
  },

  // 游戏分类点击
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category;
    console.log('选择游戏分类:', category);

    if (category.name === '更多品类') {
      this.onShowMoreGames();
    } else {
      // 跳转到对应游戏的陪玩师列表
      my.navigateTo({
        url: `/pages/game-detail/game-detail?gameId=${category.id}&gameName=${category.name}`
      });
    }
  },

  // 显示更多游戏
  onShowMoreGames() {
    my.showActionSheet({
      title: '选择游戏',
      items: [
        '王者荣耀', '英雄联盟', '和平精英', 'CSGO', '绝地求生', 
        '永劫无间', '原神', '崩坏3', '明日方舟', '第五人格'
      ],
      success: (res) => {
        console.log('选择了游戏:', res.index);
        const selectedGame = res.index;
        // 处理游戏选择逻辑
      }
    });
  },

  // 陪玩师卡片点击
  onPlayerTap(e) {
    const player = e.currentTarget.dataset.player;
    console.log('选择陪玩师:', player);

    // 跳转到下单页面
    my.navigateTo({
      url: `/pages/order/order?playerId=${player.id}`
    });
  },

  // 加载推荐陪玩师
  loadRecommendedPlayers() {
    // 这里应该调用API获取推荐陪玩师列表
    console.log('加载推荐陪玩师');
    
    // 模拟API调用
    // my.request({
    //   url: '/api/players/recommended',
    //   success: (res) => {
    //     this.setData({
    //       recommendedPlayers: res.data
    //     });
    //   },
    //   fail: (err) => {
    //     console.error('加载推荐陪玩师失败:', err);
    //   }
    // });
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadRecommendedPlayers();
    
    setTimeout(() => {
      my.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom() {
    console.log('上拉加载更多');
    this.loadMorePlayers();
  },

  // 加载更多陪玩师
  loadMorePlayers() {
    my.showLoading({
      content: '加载中...'
    });

    // 模拟加载更多数据
    setTimeout(() => {
      my.hideLoading();
      my.showToast({
        type: 'success',
        content: '加载完成'
      });
    }, 1000);
  }
});
