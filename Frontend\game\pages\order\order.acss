.order-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-icon {
  font-size: 36rpx;
  color: #333333;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.nav-right {
  font-size: 28rpx;
  color: #1677ff;
}

/* 陪玩师信息 */
.player-info {
  display: flex;
  padding: 40rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.player-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.player-details {
  flex: 1;
}

.player-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.player-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-right: 12rpx;
}

.player-title {
  font-size: 28rpx;
  color: #666666;
}

.player-stats {
  display: flex;
  align-items: center;
}

.voice-icon {
  font-size: 24rpx;
  color: #1677ff;
  margin-right: 8rpx;
}

.age {
  font-size: 24rpx;
  color: #ffffff;
  background-color: #1677ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 陪玩师介绍 */
.player-description {
  padding: 0 30rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

/* 通用section样式 */
.category-section,
.game-card-section,
.quantity-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

/* 品类选择 */
.category-badge {
  display: flex;
  align-items: center;
  background-color: #fff7e6;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ffd666;
}

.crown-icon {
  font-size: 24rpx;
  color: #fa8c16;
  margin-right: 8rpx;
}

.badge-text {
  font-size: 24rpx;
  color: #fa8c16;
  margin-right: 8rpx;
}

.arrow-icon {
  font-size: 20rpx;
  color: #999999;
}

/* 游戏名片选择 */
.help-icon {
  font-size: 24rpx;
  color: #999999;
  margin-right: 12rpx;
}

.select-hint {
  font-size: 28rpx;
  color: #999999;
  margin-right: 12rpx;
}

/* 单数选择 */
.stepper-container {
  display: flex;
  align-items: center;
}

.unit {
  font-size: 28rpx;
  color: #333333;
  margin-left: 12rpx;
}

/* 年龄确认 */
.age-confirm {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.confirm-text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 12rpx;
}

/* 备注 */
.note-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.note-input {
  margin-top: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

/* 费用显示 */
.price-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.price-label {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.price-info {
  display: flex;
  align-items: center;
}

.price {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: 500;
  margin-right: 8rpx;
}

.price-detail {
  font-size: 24rpx;
  color: #999999;
}

/* 下单按钮 */
.order-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}

.order-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}

.order-button[disabled] {
  background: #f5f5f5;
  color: #cccccc;
}
