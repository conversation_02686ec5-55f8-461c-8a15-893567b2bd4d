<view class="order-page">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <ant-icon type="ArrowLeftOutline" class="back-icon" onTap="onBack" />
    <text class="nav-title">下单</text>
    <text class="nav-right" onTap="onShowIntro">功能介绍</text>
  </view>

  <!-- 陪玩师头像和基本信息 -->
  <view class="player-info">
    <ant-image 
      src="{{playerInfo.avatar}}" 
      class="player-avatar"
      mode="aspectFill"
    />
    <view class="player-details">
      <view class="player-name-row">
        <text class="player-name">{{playerInfo.name}}</text>
        <text class="player-title">（{{playerInfo.title}}）</text>
      </view>
      <view class="player-stats">
        <ant-icon type="SoundOutline" class="voice-icon" />
        <text class="age">{{playerInfo.age}}"</text>
      </view>
    </view>
  </view>

  <!-- 陪玩师介绍 -->
  <view class="player-description">
    <text class="description-text">{{playerInfo.description}}</text>
  </view>

  <!-- 品类选择 -->
  <view class="category-section">
    <view class="section-header">
      <text class="section-title">品类</text>
      <view class="category-badge">
        <ant-icon type="CrownOutline" class="crown-icon" />
        <text class="badge-text">王者荣耀</text>
        <ant-icon type="RightOutline" class="arrow-icon" />
      </view>
    </view>
  </view>

  <!-- 游戏名片选择 -->
  <view class="game-card-section">
    <view class="section-header">
      <text class="section-title">游戏名片（选填）</text>
      <ant-icon type="QuestionCircleOutline" class="help-icon" />
      <text class="select-hint" onTap="onSelectGameCard">请选择游戏名片</text>
      <ant-icon type="RightOutline" class="arrow-icon" />
    </view>
  </view>

  <!-- 单数选择 -->
  <view class="quantity-section">
    <view class="section-header">
      <text class="section-title">单数</text>
      <view class="stepper-container">
        <ant-stepper 
          value="{{orderQuantity}}" 
          min="1" 
          max="10"
          onChange="onQuantityChange"
        />
        <text class="unit">单</text>
      </view>
    </view>
  </view>

  <!-- 年龄确认 -->
  <view class="age-confirm">
    <ant-checkbox 
      checked="{{ageConfirmed}}" 
      onChange="onAgeConfirmChange"
    />
    <text class="confirm-text">本人已成年，已知晓禁止未成年下单</text>
  </view>

  <!-- 备注 -->
  <view class="note-section">
    <text class="section-title">备注</text>
    <ant-input 
      placeholder="（选填）可备注游戏ID、YY号等用于语音交流的软件"
      value="{{orderNote}}"
      onInput="onNoteInput"
      class="note-input"
      multiline="{{true}}"
      rows="3"
    />
  </view>

  <!-- 费用显示 -->
  <view class="price-section">
    <text class="price-label">费用</text>
    <view class="price-info">
      <text class="price">{{totalPrice}}元/小时</text>
      <text class="price-detail">*{{orderQuantity}}</text>
    </view>
  </view>

  <!-- 下单按钮 -->
  <view class="order-button-container">
    <ant-button 
      type="primary" 
      class="order-button"
      onTap="onSubmitOrder"
      disabled="{{!ageConfirmed}}"
    >
      下单
    </ant-button>
  </view>
</view>
