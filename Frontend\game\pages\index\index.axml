<view class="index-page">
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <ant-search-bar placeholder="搜索游戏名，昵称，车牌号" value="{{searchValue}}" onInput="onSearchInput" onSubmit="onSearchSubmit" class="search-bar" />
    <view class="call-trainer" onTap="onCallTrainer">
      <ant-icon type="ThunderboltOutline" class="thunder-icon" />
      <text class="call-text">呼叫陪练师</text>
    </view>
  </view>

  <!-- 游戏分类网格 -->
  <view class="game-categories">
    <view class="category-grid">
      <view class="category-item" a:for="{{gameCategories}}" a:key="id" onTap="onCategoryTap" data-category="{{item}}">
        <ant-image src="{{item.icon}}" class="category-icon" mode="aspectFit" />
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 热门游戏标签 -->
  <view class="hot-games">
    <view class="hot-game-item" a:for="{{hotGames}}" a:key="id">
      <text class="hot-game-name">{{item.name}}</text>
    </view>
    <view class="more-games" onTap="onShowMoreGames">
      <text class="more-text">更多</text>
    </view>
  </view>

  <!-- 推荐大神列表 -->
  <view class="recommended-section">
    <view class="section-header">
      <text class="section-title">推荐大神</text>
      <view class="filter-options">
        <ant-icon type="FilterOutline" class="filter-icon" />
        <text class="filter-text">性别</text>
      </view>
    </view>

    <!-- 大神列表 -->
    <view class="player-list">
      <view class="player-card" a:for="{{recommendedPlayers}}" a:key="id" onTap="onPlayerTap" data-player="{{item}}">
        <!-- 播放按钮和语音时长 -->
        <view class="voice-info">
          <view class="play-button">
            <ant-icon type="PlayCircleOutline" class="play-icon" />
          </view>
          <text class="voice-duration">{{item.voiceDuration}}"</text>
        </view>

        <!-- 陪玩师头像 -->
        <ant-image src="{{item.avatar}}" class="player-avatar" mode="aspectFill" />

        <!-- 陪玩师信息 -->
        <view class="player-info">
          <view class="player-header">
            <text class="player-name">{{item.name}}</text>
            <view class="player-badges">
              <ant-icon type="ManOutline" a:if="{{item.gender === 'male'}}" class="gender-icon male" />
              <ant-icon type="WomanOutline" a:if="{{item.gender === 'female'}}" class="gender-icon female" />
              <view class="hot-badge" a:if="{{item.isHot}}">
                <ant-icon type="FireOutline" class="fire-icon" />
                <text class="hot-text">热度 {{item.hotValue}}</text>
              </view>
            </view>
          </view>

          <text class="player-description">{{item.description}}</text>

          <view class="player-footer">
            <text class="player-price">{{item.price}} 元/小时</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
