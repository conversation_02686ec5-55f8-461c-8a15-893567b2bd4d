/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 通用容器 */
.container {
  padding: 0 16px;
  background-color: #fff;
}

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 8px;
  margin: 8px 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #1677ff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
}

/* 文本样式 */
.text-primary {
  color: #1677ff;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-danger {
  color: #ff4d4f;
}

.text-success {
  color: #52c41a;
}

/* 间距工具类 */
.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.ml-8 { margin-left: 8px; }
.mr-8 { margin-right: 8px; }

.pt-8 { padding-top: 8px; }
.pt-16 { padding-top: 16px; }
.pb-8 { padding-bottom: 8px; }
.pb-16 { padding-bottom: 16px; }
.pl-8 { padding-left: 8px; }
.pr-8 { padding-right: 8px; }

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 字体大小 */
.font-12 { font-size: 12px; }
.font-14 { font-size: 14px; }
.font-16 { font-size: 16px; }
.font-18 { font-size: 18px; }
.font-20 { font-size: 20px; }

/* 字体粗细 */
.font-normal { font-weight: normal; }
.font-medium { font-weight: 500; }
.font-bold { font-weight: bold; }
