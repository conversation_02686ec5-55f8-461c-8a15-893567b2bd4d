Page({
  data: {
    playerInfo: {
      name: '雾眠',
      title: '大国野射边',
      age: '28',
      avatar: '/images/player-avatar.jpg',
      description: '幽默男大声音温柔会唱歌，国服技术陪（精壮野射边）♥双区各段可接，巅峰2300单排 80 ⭐国际羽、露娜、澜、耀、孙尚香等十多个国标，省标太多不列举了●一单包 c 三局（败方不算前三补一局）♥有国服车队'
    },
    orderQuantity: 1,
    ageConfirmed: false,
    orderNote: '',
    basePrice: 25,
    totalPrice: 25
  },

  onLoad(query) {
    console.log('下单页面加载', query);
    // 如果从首页传递了陪玩师信息，则更新数据
    if (query.playerId) {
      this.loadPlayerInfo(query.playerId);
    }
    this.calculateTotalPrice();
  },

  onShow() {
    console.log('下单页面显示');
  },

  // 返回上一页
  onBack() {
    my.navigateBack();
  },

  // 显示功能介绍
  onShowIntro() {
    my.showModal({
      title: '功能介绍',
      content: '这里是游戏陪玩下单功能，您可以选择心仪的陪玩师进行游戏陪玩服务。',
      showCancel: false
    });
  },

  // 选择游戏名片
  onSelectGameCard() {
    my.showActionSheet({
      title: '选择游戏名片',
      items: ['王者荣耀', '英雄联盟', '和平精英', 'CSGO', '绝地求生'],
      success: (res) => {
        console.log('选择了游戏名片:', res.index);
        // 这里可以更新选中的游戏名片
      }
    });
  },

  // 数量变化
  onQuantityChange(value) {
    console.log('数量变化:', value);
    this.setData({
      orderQuantity: value
    }, () => {
      this.calculateTotalPrice();
    });
  },

  // 年龄确认变化
  onAgeConfirmChange(checked) {
    console.log('年龄确认:', checked);
    this.setData({
      ageConfirmed: checked
    });
  },

  // 备注输入
  onNoteInput(e) {
    this.setData({
      orderNote: e.detail.value
    });
  },

  // 计算总价
  calculateTotalPrice() {
    const total = this.data.basePrice * this.data.orderQuantity;
    this.setData({
      totalPrice: total
    });
  },

  // 加载陪玩师信息
  loadPlayerInfo(playerId) {
    // 这里应该调用API获取陪玩师详细信息
    console.log('加载陪玩师信息:', playerId);
    // 模拟API调用
    // my.request({
    //   url: '/api/player/' + playerId,
    //   success: (res) => {
    //     this.setData({
    //       playerInfo: res.data
    //     });
    //   }
    // });
  },

  // 提交订单
  onSubmitOrder() {
    if (!this.data.ageConfirmed) {
      my.showToast({
        type: 'fail',
        content: '请确认您已成年'
      });
      return;
    }

    const orderData = {
      playerId: this.data.playerInfo.id,
      quantity: this.data.orderQuantity,
      note: this.data.orderNote,
      totalPrice: this.data.totalPrice
    };

    console.log('提交订单:', orderData);

    my.showLoading({
      content: '提交中...'
    });

    // 模拟API调用
    setTimeout(() => {
      my.hideLoading();
      my.showModal({
        title: '订单提交成功',
        content: `订单金额：${this.data.totalPrice}元\n请等待陪玩师接单`,
        showCancel: false,
        success: () => {
          // 跳转到订单详情或首页
          my.navigateBack();
        }
      });
    }, 2000);

    // 实际API调用示例
    // my.request({
    //   url: '/api/order/create',
    //   method: 'POST',
    //   data: orderData,
    //   success: (res) => {
    //     my.hideLoading();
    //     if (res.data.success) {
    //       my.showToast({
    //         type: 'success',
    //         content: '订单提交成功'
    //       });
    //       my.navigateTo({
    //         url: '/pages/order-detail/order-detail?orderId=' + res.data.orderId
    //       });
    //     } else {
    //       my.showToast({
    //         type: 'fail',
    //         content: res.data.message || '订单提交失败'
    //       });
    //     }
    //   },
    //   fail: (err) => {
    //     my.hideLoading();
    //     my.showToast({
    //       type: 'fail',
    //       content: '网络错误，请重试'
    //     });
    //   }
    // });
  }
});
